<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兑换码模态框测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 兑换码按钮样式 */
        #showRedemptionModalBtn {
            transition: all 0.3s ease;
            border-radius: 20px;
            padding: 8px 16px;
        }

        #showRedemptionModalBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* 兑换码模态框样式 */
        #modalRedemptionCodeInput {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        #modalRedemptionCodeInput:focus {
            border-color: #198754;
            box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">兑换码功能测试</h5>
                        <p class="card-text">点击下面的按钮测试新的兑换码弹出框功能</p>
                        
                        <!-- 兑换码按钮 -->
                        <button id="showRedemptionModalBtn" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-gift me-1"></i>兑换码
                        </button>
                        <small class="text-muted ms-2">点击输入兑换码获取积分奖励</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 兑换码输入模态框 -->
    <div class="modal fade" id="redemptionModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-gift me-2"></i>兑换码
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-ticket-alt text-success" style="font-size: 3rem;"></i>
                        <p class="mt-2 text-muted">输入兑换码获取积分奖励</p>
                    </div>
                    <div class="mb-3">
                        <label for="modalRedemptionCodeInput" class="form-label">兑换码</label>
                        <input type="text" id="modalRedemptionCodeInput" class="form-control form-control-lg text-center" 
                               placeholder="请输入兑换码" maxlength="20" style="letter-spacing: 2px; font-family: monospace;">
                        <div class="form-text">兑换码不区分大小写</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" id="modalRedeemBtn" class="btn btn-success">
                        <i class="fas fa-check me-1"></i>兑换
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示兑换码模态框按钮
            const showRedemptionModalBtn = document.getElementById('showRedemptionModalBtn');
            const redemptionModal = new bootstrap.Modal(document.getElementById('redemptionModal'));
            const modalRedemptionCodeInput = document.getElementById('modalRedemptionCodeInput');
            const modalRedeemBtn = document.getElementById('modalRedeemBtn');

            if (showRedemptionModalBtn) {
                showRedemptionModalBtn.addEventListener('click', function() {
                    // 清空输入框并显示模态框
                    modalRedemptionCodeInput.value = '';
                    redemptionModal.show();
                    
                    // 自动聚焦到输入框
                    setTimeout(() => {
                        modalRedemptionCodeInput.focus();
                    }, 500);
                });
            }

            // 模态框中的兑换功能
            if (modalRedeemBtn && modalRedemptionCodeInput) {
                modalRedeemBtn.addEventListener('click', function() {
                    const code = modalRedemptionCodeInput.value.trim().toUpperCase();

                    if (!code) {
                        alert('请输入兑换码');
                        modalRedemptionCodeInput.focus();
                        return;
                    }

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>兑换中...';

                    // 模拟兑换过程
                    setTimeout(() => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-check me-1"></i>兑换';
                        
                        alert(`测试兑换码: ${code}\n这是一个测试，实际应用中会连接到服务器`);
                        
                        // 清空输入框并关闭模态框
                        modalRedemptionCodeInput.value = '';
                        redemptionModal.hide();
                    }, 2000);
                });

                // 回车键兑换
                modalRedemptionCodeInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        modalRedeemBtn.click();
                    }
                });

                // 模态框显示时自动聚焦
                document.getElementById('redemptionModal').addEventListener('shown.bs.modal', function() {
                    modalRedemptionCodeInput.focus();
                });
            }
        });
    </script>
</body>
</html>
